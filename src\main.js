import AOS from 'aos';
import 'aos/dist/aos.css';
import lottie from 'lottie-web';
import { initCarousels } from "./scripts/carousel.js";
import { initGoToTopButton } from "./scripts/go-to-top.js";
import { initBannerFloatingAnimation, initBannerGSAP, initBannerHoverEffects } from "./scripts/gsap-banner.js";
import { initCarouselFloatingEffect, initCarouselHoverEffects, initCarouselSectionGSAP } from "./scripts/gsap-carousel-section.js";
import { initCustomersGSAP } from "./scripts/gsap-customers.js";
import { initFeatureHoverEffects, initFeaturesGSAP, initFloatingIconsAnimation } from "./scripts/gsap-features.js";
import { initHeaderGSAP } from "./scripts/gsap-header.js";
import { replaySVGLogoAnimation } from "./scripts/gsap-svg-logo.js";
import { initHeroGSAP } from "./scripts/gsap-hero.js";
import { initGridHoverEffects, initGridSlidingAnimation, initServiceHoverEffects, initServicesGSAP } from "./scripts/gsap-services.js";
import { initFooterGSAP, initSimpleFooterGSAP } from "./scripts/gsap-footer.js";
import { initWorksGSAP } from "./scripts/gsap-works.js";
import { initBlogGSAP } from "./scripts/gsap-blog.js";
import { initProductsGSAP } from "./scripts/gsap-products.js";
import { initServicesHoverEffect } from "./scripts/services-hover.js";
import { initAchievementsHoverEffect } from "./scripts/achievements-hover.js";
import { initAccordion } from "./scripts/accordion.js";
import { initContactCardsAnimation } from "./scripts/contact-cards-animation.js";
import { initContactFormValidation } from "./scripts/contact-form-validation.js";
import { initJobApplicationForm } from "./scripts/job-application-form.js";
import { initJobDescriptionAnimations, initJobDescriptionHoverEffects, initJobDescriptionTextAnimations, initJobDescriptionParallax } from "./scripts/job-description-animations.js";
import { initQuotationForm } from "./scripts/quotation-form.js";
import { initSocialMediaForm } from "./scripts/social-media-form.js";
import { initWebDesignAnimations } from "./scripts/web-design-animations.js";
import { initWebDesignHoverEffects, initWebDesignFloatingAnimations } from "./scripts/web-design-hover-effects.js";
import { initHostingPlansAnimation } from "./scripts/hosting-plans-animation.js";
import { initContactSectionAnimation } from "./scripts/contact-section-animation.js";
import { initNewsletterForm } from "./scripts/newsletter-form.js";
import { initEntrepreneurshipAnimation } from "./scripts/entrepreneurship-animation.js";
import { initTestimonialsCarousel } from "./scripts/testimonials-carousel.js";
import { initTemplatesFilter } from "./scripts/templates-filter.js";
import { initTemplatesCarousel } from "./scripts/templates-carousel.js";
import { initWorksFilter } from "./scripts/works-filter.js";
import { initScrollWorksAnimation, initScrollWorksAnimationEnhanced } from "./scripts/scroll-works-animation.js";
import { initLaptopScrollAnimation } from "./scripts/laptop-scroll-animation.js";
import { initDragDropFeatures } from "./scripts/drag-drop-features.js";
import { initAboutAnimations, initAboutHoverEffects } from "./scripts/about-animations.js";

import { initLanguageToggle, initNavigationHover } from "./scripts/header.js";
import { initBurgerMenu } from "./scripts/burger-menu.js";
import { initSmoothScrolling } from "./scripts/smooth-scrolling.js";
import "./style.css";

import { gsap } from "gsap";

import { DrawSVGPlugin } from "gsap/DrawSVGPlugin";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { SplitText } from "gsap/SplitText";





document.addEventListener("DOMContentLoaded", () => {

  // Lottie animation initialization
  const lottieContainer = document.getElementById('lottie-animation');
  const lottieContainerForServerAnimation = document.getElementById('lottie-animation-server');
  if (lottieContainer) {
    lottie.loadAnimation({
      container: lottieContainer, // the dom element
      renderer: 'svg',
      loop: true,
      autoplay: true,
      path: '/public/pages/web-design/spinning-computer.json' // adjust the path to your JSON file
    });
  }
  if (lottieContainerForServerAnimation) {
    lottie.loadAnimation({
      container: lottieContainerForServerAnimation, // the dom element
      renderer: 'svg',
      loop: true,
      autoplay: true,
      path: '/public/pages/web-hosting/working-server.json' // adjust the path to your JSON file
    });
  }
  gsap.registerPlugin(DrawSVGPlugin, ScrollTrigger, SplitText);

  // Initialize GSAP animations with optimized timing
  const headerTimeline = initHeaderGSAP();
  const heroTimeline = initHeroGSAP();

  // Start hero animation 0.5 seconds before header completes (at 2.5s)
  setTimeout(() => {
    heroTimeline.play();
  }, 2500);

  // Initialize scroll-triggered customer section animation
  initCustomersGSAP();

  // Initialize products section animation
  initProductsGSAP();

  // Initialize achievements hover effect
  initAchievementsHoverEffect();

  // Initialize accordion functionality
  initAccordion();



  // Initialize contact cards animation
  initContactCardsAnimation();

  // Initialize contact form validation
  initContactFormValidation();

  // Initialize job application form
  initJobApplicationForm();

  // Initialize job description page animations
  initJobDescriptionAnimations();
  initJobDescriptionHoverEffects();

  // Initialize quotation request form
  initQuotationForm();

  // Initialize social media requirements form
  initSocialMediaForm();

  // Initialize contact section animation
  initContactSectionAnimation();

  // Initialize newsletter form
  initNewsletterForm();

  // Initialize entrepreneurship section animation
  initEntrepreneurshipAnimation();

  // Initialize carousel section animations
  initCarouselSectionGSAP();
  initCarouselHoverEffects();
  initCarouselFloatingEffect();

  // Initialize services section animations
  initServicesGSAP();
  initGridSlidingAnimation();
  initServiceHoverEffects();
  initGridHoverEffects();
  initServicesHoverEffect();

  // Initialize works section animations
  initWorksGSAP();

  // Initialize features section animations
  initFeaturesGSAP();
  initFloatingIconsAnimation();
  initFeatureHoverEffects();

  // Initialize drag and drop features functionality
  initDragDropFeatures();

  // Initialize blog section animations
  initBlogGSAP();

  // Initialize banner section animations
  initBannerGSAP();
  initBannerFloatingAnimation();
  initBannerHoverEffects();

  // Keep AOS for other sections (will be replaced gradually)
  AOS.init({
    duration: 800,
    once: true,
    offset: 100
  });

  initSmoothScrolling();
  initGoToTopButton();
  initLanguageToggle();
  initNavigationHover();
  initBurgerMenu();
  initCarousels();

  // Initialize projects scroll fade animation - EXACT WORKING EXAMPLE
  initProjectsScrollAnimation();

  // Initialize scroll-triggered works animation
  initScrollWorksAnimation();

  // Initialize newsletter form validation
  initNewsletterValidation();

  // Initialize testimonials carousel
  initTestimonialsCarousel();

  // Initialize templates filter
  initTemplatesFilter();

  // Initialize templates carousel
  initTemplatesCarousel();

  // Initialize works filter for our-business page
  initWorksFilter();

  // Initialize laptop scroll animation
  initLaptopScrollAnimation();

  // Initialize web design page animations
  initWebDesignAnimations();

  // Initialize web design hover effects
  initWebDesignHoverEffects();

  // Initialize web design floating animations
  initWebDesignFloatingAnimations();

  // Initialize hosting plans animations
  initHostingPlansAnimation();

  // Initialize about page animations
  initAboutAnimations();

  // Initialize about page hover effects
  initAboutHoverEffects();

  // Initialize footer animations with fallback
  try {
    initFooterGSAP();
  } catch (error) {
    console.warn('Main footer animation failed, using simple fallback:', error);
    initSimpleFooterGSAP();
  }

});

// Projects scroll animation - EXACT WORKING EXAMPLE
function initProjectsScrollAnimation() {
  // Register GSAP plugins
  gsap.registerPlugin(ScrollTrigger);

  // Title animation
  gsap.fromTo("[data-scroll-trigger='title']",
    {
      opacity: 0,
      y: 100,
      scale: 0.8
    },
    {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 1.5,
      ease: "power3.out",
      scrollTrigger: {
        trigger: "[data-scroll-trigger='title']",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    }
  );

  // Project cards animations
  const projectCards = document.querySelectorAll('.project-card');

  projectCards.forEach((card, index) => {
    // Entrance animation
    gsap.fromTo(card,
      {
        opacity: 0,
        y: 100,
        scale: 0.8,
        rotationX: 15
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        rotationX: 0,
        duration: 1.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: card,
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Parallax effect for the card
    gsap.to(card, {
      y: -50,
      ease: "none",
      scrollTrigger: {
        trigger: card,
        start: "top bottom",
        end: "bottom top",
        scrub: 1
      }
    });

    // Image parallax effect
    const cardImage = card.querySelector('img');
    if (cardImage) {
      gsap.to(cardImage, {
        y: -30,
        ease: "none",
        scrollTrigger: {
          trigger: card,
          start: "top bottom",
          end: "bottom top",
          scrub: 1.5
        }
      });
    }

    // Fade out as it goes up
    gsap.to(card, {
      opacity: 0.3,
      scale: 0.95,
      ease: "none",
      scrollTrigger: {
        trigger: card,
        start: "bottom 60%",
        end: "bottom 20%",
        scrub: 1,
        toggleActions: "none none none reverse"
      }
    });
  });

  // Add smooth scroll behavior to buttons
  document.querySelectorAll('.project-details-btn').forEach(btn => {
    btn.addEventListener('click', function (e) {
      e.preventDefault();

      // Add click animation
      gsap.to(this, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });

    });
  });

  // Refresh ScrollTrigger on window resize
  window.addEventListener('resize', () => {
    ScrollTrigger.refresh();
  });

}

// Newsletter form validation
function initNewsletterValidation() {
  const emailInput = document.getElementById('newsletter-email');
  const errorDiv = document.getElementById('email-error');
  const successDiv = document.getElementById('newsletter-success');

  if (!emailInput || !errorDiv) {
    return;
  }

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Real-time validation on input
  emailInput.addEventListener('input', function () {
    const email = this.value.trim();

    // Clear previous states
    errorDiv.classList.add('hidden');
    successDiv.classList.add('hidden');

    if (email === '') {
      // Empty input - reset to default state
      this.classList.remove('border-red-400', 'border-green-400');
      this.classList.add('border-gray-400');
      return;
    }

    if (!emailRegex.test(email)) {
      // Invalid email - show error
      this.classList.remove('border-gray-400', 'border-green-400');
      this.classList.add('border-red-400');
      errorDiv.textContent = 'يرجى إدخال بريد إلكتروني صحيح';
      errorDiv.classList.remove('hidden');
    } else {
      // Valid email - show success state
      this.classList.remove('border-gray-400', 'border-red-400');
      this.classList.add('border-green-400');
      errorDiv.classList.add('hidden');

      // Show success message
      successDiv.classList.remove('hidden');
    }
  });

  // Focus and blur effects
  emailInput.addEventListener('focus', function () {
    if (!this.classList.contains('border-red-400') && !this.classList.contains('border-green-400')) {
      this.classList.remove('border-gray-400');
      this.classList.add('border-primary-yellow');
    }
  });

  emailInput.addEventListener('blur', function () {
    if (!this.classList.contains('border-red-400') && !this.classList.contains('border-green-400')) {
      this.classList.remove('border-primary-yellow');
      this.classList.add('border-gray-400');
    }
  });

  // Footer logo hover effect - same as header logo
  function initFooterLogoHoverEffect() {
    const footerLogoContainer = document.querySelector('#footer-logo');
    let isAnimating = false;

    if (footerLogoContainer) {
      // Mouse enter - trigger full SVG animation
      footerLogoContainer.addEventListener("mouseenter", () => {
        if (!isAnimating) {
          isAnimating = true;
          const hoverAnimation = replaySVGLogoAnimation();

          hoverAnimation.eventCallback("onComplete", () => {
            isAnimating = false;
          });
        }
      });
    }
  }

  // Initialize footer logo hover effect
  initFooterLogoHoverEffect();

}